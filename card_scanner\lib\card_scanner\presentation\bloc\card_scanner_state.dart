import 'package:equatable/equatable.dart';
import 'package:card_scanner/card_scanner/domain/model/client_domain_model.dart';

abstract class CardScannerState extends Equatable {
  const CardScannerState();

  @override
  List<Object?> get props => [];
}

class CardScannerInitial extends CardScannerState {
  const CardScannerInitial();
}

class CardScannerLoading extends CardScannerState {
  const CardScannerLoading();
}

class CardScannerSuccess extends CardScannerState {
  final ClientDomainModel client;

  const CardScannerSuccess({required this.client});

  @override
  List<Object> get props => [client];
}

class CardScannerError extends CardScannerState {
  final String message;

  const CardScannerError({required this.message});

  @override
  List<Object> get props => [message];
}
