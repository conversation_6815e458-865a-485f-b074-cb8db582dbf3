import 'package:card_scanner/card_scanner/data/model/ocr_text.dart';
import 'package:card_scanner/card_scanner/domain/model/card_domain_model.dart';

class CardDataToDomainMapper {
  
  CardDomainModel toDomain( OcrText scan ) {
    // Simple regex to find card number
    final cardNumberRegExp = RegExp(r'\b\d{13,19}\b');
    final expiryDateRegExp = RegExp(r'(0[1-9]|1[0-2])\/?([0-9]{2}|[0-9]{4})');

    final numberMatch = cardNumberRegExp.firstMatch(scan.rawText);
    final expiryMatch = expiryDateRegExp.firstMatch(scan.rawText);

    return CardDomainModel(
      number: numberMatch?.group(0) ?? '',
      expiryDate: expiryMatch?.group(0) ?? '',
    );
  }
}
