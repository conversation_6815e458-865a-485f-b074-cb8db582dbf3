const { validationResult, body } = require('express-validator');
const User = require('../models/User');

// Desactivate agent account
exports.desactivateAgent = async (req, res) => {
  try {
    const { id } = req.params;

    const agent = await User.findOne({ _id: id, role: 'agent' });
     if (!agent) {
      return res.status(404).json({ error: 'Agent not found' });
    }

    agent.status = 'inactive';
    await agent.save();

    res.status(200).json({ message: 'Agent account deactivated successfully' });
    console.log("The account status: ", agent.status);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Update agent account (except password)
exports.updateAgent = async (req, res) => {
  try {
    const agentId = req.params.id;
    const updates = req.body;

    const updatedAgent = await User.findByIdAndUpdate(
      agentId,
      updates,
      { new: true, runValidators: true }
    )

    if (!updatedAgent) {
      return res.status(404).json({ message: 'Agent not found' });
    }

    res.json({ message: 'Agent updated successfully', agent: updatedAgent });
  } catch (error) {
    res.status(500).json({ message: 'Error updating agent', error });
  }
};

// Get agents list
exports.getAllAgents = async (req, res) => {
  try {
    const agents = await User.find({ role: 'agent' });
    res.json(agents);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Get agents by branch 
exports.getAgentsByBranch = async (req,res) => {
 try {
    const branchId = req.params.branchId;

    if (!branchId) {
      return res.status(400).json({ message: 'Branch ID is required' });
    }

    const agents = await User.find({ branch: branchId });

    if (!agents || agents.length === 0) {
      return res.status(404).json({ message: 'No agents found for this branch' });
    }

    res.status(200).json({ agents });
  } catch (error) {
    console.error('Error fetching agents by branch:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};