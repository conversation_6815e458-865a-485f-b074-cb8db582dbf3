import 'package:card_scanner/card_scanner/data/card_scanner_repo_impl.dart';
import 'package:card_scanner/card_scanner/data/mapper/card_data_to_domain_mapper.dart';
import 'package:card_scanner/card_scanner/data/mapper/client_data_to_domain_mapper.dart';
import 'package:card_scanner/card_scanner/data/service/api_service.dart';
import 'package:card_scanner/card_scanner/data/service/ocr_service.dart';
import 'package:card_scanner/card_scanner/domain/card_scanner_repository.dart';
import 'package:card_scanner/card_scanner/domain/usecase/scan_card_usecase.dart';
import 'package:card_scanner/card_scanner/presentation/bloc/card_scanner_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:image_picker/image_picker.dart';

// DI instance
final sl = GetIt.instance;

Future<void> init() async {
  // Mappers
  sl.registerLazySingleton<CardDataToDomainMapper>(
    () => CardDataToDomainMapper(),
  );
  sl.registerLazySingleton<ClientDataToDomainMapper>(
    () => ClientDataToDomainMapper(),
  );

  // Services
  sl.registerLazySingleton<OcrService>(() => OcrService());
  sl.registerLazySingleton<ApiService>(
    () => ApiService(baseUrl: "http://192.168.100.60:5000/api"),
  );

  // Repository
  sl.registerLazySingleton<CardScannerRepository>(
    () => CardScannerRepoImpl(
      ocrService: sl<OcrService>(),
      apiService: sl<ApiService>(),
      ocrMapper: sl<CardDataToDomainMapper>(),
      clientMapper: sl<ClientDataToDomainMapper>(),
    ),
  );

  // Use Case
  sl.registerLazySingleton(() => ScanCardUsecase(sl<CardScannerRepository>()));

  // External Dependencies
  sl.registerLazySingleton<ImagePicker>(() => ImagePicker());

  // BLoC
  sl.registerFactory(
    () => CardScannerBloc(
      scanCardUsecase: sl<ScanCardUsecase>(),
      imagePicker: sl<ImagePicker>(),
    ),
  );
}
