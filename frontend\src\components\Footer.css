.footer {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: #ffffff;
  padding: 3rem 0 1.5rem;
  margin-top: auto;
  width: 100%;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 6rem;
  gap: 3rem;
  width: 100%;
  box-sizing: border-box;
}

.footer-left {
  flex: 1;
  max-width: 600px;
}

.footer-logo {
  margin-bottom: 1.5rem;
}

.footer-logo-img {
  height: 50px;
  width: auto;
  filter: brightness(0) invert(1);
}

.footer-description {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: #bdc3c7;
  max-width: 500px;
}

.footer-links {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}

.footer-links a {
  color: #bdc3c7;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #ffffff;
}

.footer-right {
  flex-shrink: 0;
  text-align: right;
}

.download-text {
  font-size: 16px;
  margin-bottom: 1rem;
  color: #ffffff;
  font-weight: 500;
}

.app-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-end;
}

.app-button {
  display: block;
  transition: transform 0.2s ease;
}

.app-button:hover {
  transform: scale(1.05);
}

.app-button img {
  height: 40px;
  width: auto;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding: 1.5rem 6rem 0;
  border-top: 1px solid #34495e;
  width: 100%;
  box-sizing: border-box;
}

.footer-bottom p {
  font-size: 14px;
  color: #95a5a6;
  margin: 0;
}

.social-icons {
  display: flex;
  gap: 1rem;
}

.social-icons a {
  color: #95a5a6;
  transition: color 0.3s ease, transform 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.social-icons a:hover {
  color: #ffffff;
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.2);
}

.social-icons svg {
  width: 20px;
  height: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer {
    padding: 2rem 1rem 1rem;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 2rem;
  }
  
  .footer-right {
    text-align: left;
  }
  
  .app-buttons {
    align-items: flex-start;
  }
  
  .footer-links {
    gap: 1rem;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .social-icons {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-links {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .footer-links a {
    font-size: 13px;
  }
  
  .footer-description {
    font-size: 14px;
  }
  
  .download-text {
    font-size: 14px;
  }
}
