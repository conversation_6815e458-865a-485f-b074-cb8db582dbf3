const bcrypt = require('bcrypt');
const { validationResult, body } = require('express-validator');
const User = require('../models/User');
const jwt = require('jsonwebtoken');

exports.login = [
    body('email').isEmail().normalizeEmail(),
    body('password').isLength({ min: 6 }).trim().escape(),

    async (req, res) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const { email, password } = req.body;

        try {
            let user = await User.findOne({ email });

            if (!user) {
                return res.status(400).json({ message: 'Invalid credentials' });
            }

            if (user.status === 'inactive') {
                return res.status(403).json({ message: 'Your account is deactivated. Please contact the administrator.' });
            }

            const isMatch = await bcrypt.compare(password, user.password);
            if (!isMatch) {
                return res.status(400).json({ message: 'Invalid credentials' });
            }

            const payload = {
                user: {
                    id: user.id,
                    email: user.email,
                    role: user.role
                }
            };

            jwt.sign(
                payload,
                process.env.JWT_SECRET,
                { expiresIn: '1h' },
                (err, token) => {
                    if (err) throw err;
                    req.session.token = token;
                    res.json({ message: 'Login successful', token });
                }
            );

        } catch (error) {
            console.error('Error during login:', error);
            res.status(500).json({ message: 'Server error' });
        }
    }
];

exports.logout = (req, res) => {
    console.log("Session before destruction:", req.session); // Log session data
  
    req.session.destroy((err) => {
      if (err) {
        console.error("Error destroying session:", err);
        return res.status(500).json({ message: "Server error" });
      }
  
      console.log("Session destroyed successfully");
  
  

      res.clearCookie("connect.sid");
      res.clearCookie("token");
      res.json({ message: "Logout successful" });
    });
  };

