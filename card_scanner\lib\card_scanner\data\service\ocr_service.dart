import 'package:card_scanner/card_scanner/data/model/ocr_text.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';

class OcrService {
  final TextRecognizer textRecognizer;

  OcrService()
    : textRecognizer = TextRecognizer(script: TextRecognitionScript.latin);

  Future<OcrText> scanCardImage(String imagePath) async {
    try {
      final inputImage = InputImage.fromFilePath(imagePath);
      final recognizedText = await textRecognizer.processImage(inputImage);

      final rawText = recognizedText.blocks.map((b) => b.text).join(' ');
      print(rawText);

      return OcrText(rawText: rawText);
    } catch (e) {
      return OcrText(rawText: '');
    }
  }

  void dispose() {
    textRecognizer.close();
  }
}
