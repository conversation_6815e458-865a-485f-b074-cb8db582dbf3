.navbar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 1rem 6rem;
  border-bottom: 1px solid #eee;
  background-color: white;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 3rem;
}

.logo {
  height: 60px;
  width: auto;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-links a {
  color: #2c3e50;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: color 0.2s ease;
}

.nav-links a:hover {
  color: #3498db;
  text-decoration: none;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn-outline {
  padding: 0.5rem 1rem;
  border: 1px solid #ccc;
  background: white;
  color: #2c3e50;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
}

.btn-demo {
  padding: 0.5rem 1rem;
  background: #fceef0;
  color: #a2002d;
  font-weight: 600;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.4rem;
}

.play-icon {
  font-size: 0.9rem;
}
