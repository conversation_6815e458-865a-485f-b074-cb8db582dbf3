import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:card_scanner/card_scanner/domain/usecase/scan_card_usecase.dart';
import 'card_scanner_event.dart';
import 'card_scanner_state.dart';

class CardScannerBloc extends Bloc<CardScannerEvent, CardScannerState> {
  final ScanCardUsecase scanCardUsecase;
  final ImagePicker imagePicker;

  CardScannerBloc({
    required this.scanCardUsecase,
    required this.imagePicker,
  }) : super(const CardScannerInitial()) {
    on<ScanCardFromCamera>(_onScanCardFromCamera);
    on<ScanCardFromGallery>(_onScanCardFromGallery);
    on<ResetScanner>(_onResetScanner);
  }

  Future<void> _onScanCardFromCamera(
    ScanCardFromCamera event,
    Emitter<CardScannerState> emit,
  ) async {
    await _scanCard(ImageSource.camera, emit);
  }

  Future<void> _onScanCardFromGallery(
    ScanCardFromGallery event,
    Emitter<CardScannerState> emit,
  ) async {
    await _scanCard(ImageSource.gallery, emit);
  }

  Future<void> _scanCard(
    ImageSource source,
    Emitter<CardScannerState> emit,
  ) async {
    try {
      emit(const CardScannerLoading());

      final XFile? image = await imagePicker.pickImage(source: source);
      
      if (image == null) {
        emit(const CardScannerInitial());
        return;
      }

      final client = await scanCardUsecase.call(image.path);
      emit(CardScannerSuccess(client: client));
    } catch (e) {
      emit(CardScannerError(message: e.toString()));
    }
  }

  void _onResetScanner(
    ResetScanner event,
    Emitter<CardScannerState> emit,
  ) {
    emit(const CardScannerInitial());
  }
}
