import 'package:equatable/equatable.dart';

abstract class CardScannerEvent extends Equatable {
  const CardScannerEvent();

  @override
  List<Object> get props => [];
}

class ScanCardFromCamera extends CardScannerEvent {
  const ScanCardFromCamera();
}

class ScanCardFromGallery extends CardScannerEvent {
  const ScanCardFromGallery();
}

class ResetScanner extends CardScannerEvent {
  const ResetScanner();
}
