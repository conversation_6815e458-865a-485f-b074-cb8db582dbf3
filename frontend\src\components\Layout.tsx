import React from 'react';
import Navbar from './Navbar';
import Footer from './Footer';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      minHeight: '100vh',
      backgroundColor: '#f8f9fa'
    }}>
      <Navbar />
      <main style={{
        flex: 1,
        padding: '2rem',
        backgroundColor: '#f8f9fa'
      }}>
        {children}
      </main>
      <Footer />
    </div>
  );
};

export default Layout;
