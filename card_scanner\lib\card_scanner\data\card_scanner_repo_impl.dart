import 'package:card_scanner/card_scanner/data/mapper/card_data_to_domain_mapper.dart';
import 'package:card_scanner/card_scanner/data/mapper/client_data_to_domain_mapper.dart';
import 'package:card_scanner/card_scanner/data/service/api_service.dart';
import 'package:card_scanner/card_scanner/data/service/ocr_service.dart';
import 'package:card_scanner/card_scanner/domain/card_scanner_repository.dart';
import 'package:card_scanner/card_scanner/domain/model/client_domain_model.dart';

class CardScannerRepoImpl implements CardScannerRepository {

  final OcrService ocrService;
  final ApiService apiService;
  final CardDataToDomainMapper ocrMapper;
  final ClientDataToDomainMapper clientMapper;

  CardScannerRepoImpl({
    required this.ocrService,
    required this.apiService,
    required this.ocrMapper,
    required this.clientMapper,
  });

  @override
  Future<ClientDomainModel> scanCardAndFetchClient(String imagePath) async {
     // Step 1: Use OCR to extract text from the image
    final ocr = await ocrService.scanCardImage(imagePath);

    // Step 2: Map OCR result to a domain model
    final creditCard = ocrMapper.toDomain(ocr);

    // Step 3: Use API service to fetch client details from the card data
    final clientApi = await apiService.getClientByCardNumber(creditCard.number);

    // Step 4: Map API response to domain model
    final client = clientMapper.toDomain(clientApi);

    return client;
  }

}