import 'dart:convert';
import 'package:card_scanner/card_scanner/data/model/client_data_model.dart';
import 'package:http/http.dart' as http;

class ApiService {
  final String baseUrl;

  ApiService({required this.baseUrl});

  Future<ClientDataModel> getClientByCardNumber(String cardNumber) async {
    final response = await http.get(
      Uri.parse('$baseUrl/agent/card/$cardNumber'),
    );
    print('Card number: $cardNumber');

    if (response.statusCode == 200) {
      final jsonData = jsonDecode(response.body);
      return ClientDataModel.fromJson(jsonData);
    } else {
      throw Exception('Failed to fetch client');
    }
  }
}
