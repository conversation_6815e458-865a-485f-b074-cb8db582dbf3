import 'package:flutter/material.dart';
import 'card_scanner/di/app_module.dart' as di;
import 'card_scanner/presentation/pages/card_scanner_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await di.init(); // Initialize dependencies

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Card Scanner',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const CardScannerPage(),
    );
  }
}
