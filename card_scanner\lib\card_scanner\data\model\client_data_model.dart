class ClientDataModel {
  final String id;
  final String name;
  final String cardNumber;
  final String email;

  ClientDataModel({
    required this.id,
    required this.name,
    required this.cardNumber,
    required this.email,
  });

  factory ClientDataModel.fromJson(Map<String, dynamic> json) {
    return ClientDataModel(
      id: json['id'] as String,
      name: json['name'] as String,
      cardNumber: json['cardNumber'] as String,
      email: json['email'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'cardNumber': cardNumber,
      'email': email,
    };
  }  
}
